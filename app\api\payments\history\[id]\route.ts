import { NextResponse } from "next/server";
import { getSession } from "@/lib/auth/server";
import { prisma } from "@/lib/config/prisma";

export async function GET(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession();
    const userId = session?.user?.id;
    
    // Ekstrak id dari params dengan await
    const { id } = await params;

    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Periksa apakah rental milik user yang login
    const rental = await prisma.rental.findUnique({
      where: {
        id: id,
        userId
      }
    });

    if (!rental) {
      return NextResponse.json({ error: "Not found" }, { status: 404 });
    }

    // Ambil data pembayaran terkait rental
    const payment = await prisma.payment.findFirst({
      where: {
        rentalId: id
      },
      select: {
        id: true,
        status: true,
        amount: true,
        deposit: true,
        remaining: true,
        overtimeCost: true,
        createdAt: true,
        updatedAt: true
      }
    });

    if (!payment) {
      // Jika belum ada pembayaran, kembalikan array kosong
      return NextResponse.json([]);
    }

    // Buat array riwayat pembayaran
    const history = [];

    // Jika ada pembayaran deposit
    if (payment.status === "DEPOSIT_PAID" || payment.status === "FULLY_PAID") {
      history.push({
        id: `${payment.id}-deposit`,
        type: "deposit",
        amount: payment.deposit,
        status: "fully_paid",
        createdAt: payment.createdAt,
        method: "Transfer Bank"
      });
    }

    // Jika pembayaran sudah lunas
    if (payment.status === "FULLY_PAID") {
      history.push({
        id: `${payment.id}-final`,
        type: "final",
        amount: payment.remaining + (payment.overtime || 0),
        status: "fully_paid",
        createdAt: payment.updatedAt,
        method: "Transfer Bank"
      });
    }

    return NextResponse.json(history);
  } catch (error) {
    console.error("Error fetching payment history:", error);
    return NextResponse.json(
      { error: "Failed to fetch payment history" },
      { status: 500 }
    );
  }
} 