import { prisma } from "@/lib/config/prisma";
import { sortOperationsByStatus } from "@/lib/utils/operation-status";

/**
 * Mengambil data operasi berdasarkan ID rental
 * @param id ID rental
 * @returns Data operasi rental
 */
export async function getOperationById(id: string) {
  try {
    const operation = await prisma.rental.findUnique({
      where: { id },
      select: {
        id: true,
        userId: true,
        status: true,
        startDate: true,
        endDate: true,
        operationalStart: true,
        operationalEnd: true,
        overtimeHours: true,
        amount: true,
        quantity: true,
        arrivalTime: true,
        location: true,
        address: true,
        purpose: true,
        notes: true,
        duration: true,
        product: {
          select: {
            id: true,
            name: true,
            capacity: true,
            price: true,
            overtimeRate: true,
            imageUrl: true,
            image: true,
          }
        },
        payment: {
          select: {
            id: true,
            status: true,
            deposit: true,
            remaining: true,
            overtimeCost: true,
            amount: true,
          }
        }
      }
    });

    return operation;
  } catch (error) {
    console.error("Error fetching operation data:", error);
    throw new Error("Gagal mengambil data operasi");
  }
}

/**
 * Mendapatkan daftar operasi untuk user tertentu
 * @param userId ID user
 * @returns Daftar operasi user (sorted by status priority)
 */
export async function getUserOperations(userId: string) {
  try {
    const operations = await prisma.rental.findMany({
      where: {
        userId,
        OR: [
          { operationalStart: { not: null } },
          { status: { in: ["CONFIRMED", "ACTIVE", "COMPLETED"] } }
        ]
      },
      include: {
        product: true,
        payment: true
      }
    });

    // Sort operations by status priority (pending first, completed last)
    return sortOperationsByStatus(operations);
  } catch (error) {
    console.error("Error fetching user operations:", error);
    throw new Error("Gagal mengambil daftar operasi");
  }
}

/**
 * Mendapatkan daftar semua operasi (untuk admin)
 * @returns Daftar semua operasi (sorted by status priority)
 */
export async function getAllOperations() {
  try {
    const operations = await prisma.rental.findMany({
      where: {
        OR: [
          { operationalStart: { not: null } },
          { status: { in: ["CONFIRMED", "ACTIVE", "COMPLETED"] } }
        ]
      },
      include: {
        product: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true
          }
        },
        payment: true
      }
    });

    // Sort operations by status priority (pending first, completed last)
    return sortOperationsByStatus(operations);
  } catch (error) {
    console.error("Error fetching all operations:", error);
    throw new Error("Gagal mengambil daftar operasi");
  }
}
