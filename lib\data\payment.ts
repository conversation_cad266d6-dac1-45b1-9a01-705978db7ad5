import { prisma } from "@/lib/config/prisma";
import { Prisma } from "@prisma/client";

// Simplified include to avoid errors with null user relationships
const paymentInclude = {
    rental: {
        include: {
            // user dihapus untuk menghindari error null
            product: {
                select: {
                    id: true,
                    name: true,
                    image: true,
                    price: true,
                    capacity: true,
                },
            },
        },
    },
};

export async function getPayments(): Promise<Prisma.PaymentGetPayload<{
    include: typeof paymentInclude
}>[]> {
    try {
        const payments = await prisma.payment.findMany({
            include: paymentInclude,
            orderBy: {
                createdAt: "desc",
            },
        });

        return payments;
    } catch (error) {
        console.error("Error fetching payments:", error);
        throw new Error("Gagal mengambil data pembayaran");
    }
}

export async function getPaymentById(id: string): Promise<Prisma.PaymentGetPayload<{
    include: typeof paymentInclude
}>> {
    try {
        const payment = await prisma.payment.findUnique({
            where: { id },
            include: paymentInclude,
        });

        if (!payment) {
            throw new Error("Pembayaran tidak ditemukan");
        }

        return {
            ...payment,
            userId: payment.userId || (payment.rental ? payment.rental.userId : "-")
        };
    } catch (error) {
        console.error("Error fetching payment:", error);
        throw new Error("Gagal mengambil detail pembayaran");
    }
}
